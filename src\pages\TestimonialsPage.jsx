import React, { useEffect, useState, useRef } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay } from "swiper/modules";
import "swiper/css";
 
// IMPORT YOUR LOCAL LOGO FILES HERE
// Assuming the logos are directly inside the 'Asserts' folder
// import ansourceLogo from "../Asserts/ansrsource.png";    // Your local Ansource logo
import redwood<PERSON>ogo from "../Asserts/client-redwood.webp";  
import Picture13 from "../Asserts/Picture13.png";  
import testlogo1 from "../Asserts/testlogo1.jpg";  // Your local Redwood logo
// import finastraLogo from "../Asserts/finastra.svg";    // Your local Finastra logo
 
// If you later add a logo for 'FinPoint', you would add an import here like:
// import finpointLogo from "../Asserts/finpoint_logo.png"; // Adjusted path based on your current imports
 
const ClientTestimonials = () => {
  const testimonials = [
    {
      name: "<PERSON><PERSON>",
      role: "President & CEO, Ansrsource",
      // photo: "https://ansrsource.com/wp-content/uploads/2022/06/Headshots-1.png", // This photo property is not displayed in the current layout
      logo: testlogo1, // Using the imported local Ansource logo
      review:
        "Makonis consistently stands out because they genuinely listen. They understand your business's current state and challenges, then expertly guide you to talent that fits both your capability needs and your organizational culture. I've partnered with Sanjay and his team for years and consider them invaluable to my organization's success.",
    },
    {
      name: "Ashish Joshi",
      role: "COO, Redwood Software Inc.",
      // photo: "https://cdn.theorg.com/8589a507-b587-45d7-bd11-ee05879622d3_thumb.jpg", // This photo property is not displayed in the current layout
      logo: redwoodLogo, // Using the imported local Redwood logo
      review:
        "We highly recommend Makonis to any employer looking for hiring top talent. They are professional, genuine and highly invested in finding you the best talent. We can count on them in finding right talent within time and budget.",
    },
    {
      name: "Sudhakar Krishnamachari",
      role: "Head of Engineering, LoanIQ",
      // photo: "https://randomuser.me/api/portraits/men/40.jpg", // This photo property is not displayed in the current layout
      logo: redwoodLogo, // Using the imported local Finastra logo (for LoanIQ)
      review:
        "Taking over Finastra's LoanIQ engineering in 2019, I found traditional hiring too slow. Makonis quickly supplied quality contract-to-hire engineers for testing and programming, even boosting their training investment. Our collaboration has been very positive, I wish them ongoing success.",
    },
    {
      name: "Katie Weiland",
      role: "Program Manager, Abbvie",
      // photo: "https://randomuser.me/api/portraits/women/44.jpg", // This photo property is not displayed in the current layout
      logo: Picture13, // Set to null as no specific local logo was provided for FinPoint
      // If you add a 'finpointLogo' import, you would use it here: logo: finpointLogo,
      review:
        "Makonis has been an exceptional partner, providing talented DevOps engineers who quickly grasped our needs and integrated seamlessly with our team. Their deep expertise, proactive problem-solving, and commitment to innovation have consistently elevated our projects. Communication with Makonis is always clear and collaborative, making them an invaluable asset to our organization’s success.",
    },
  ];
 
  const [expandedIndex, _setExpandedIndex] = useState(null);
  const expandedIndexRef = useRef(expandedIndex);
  const swiperRef = useRef(null);
  const timeoutRef = useRef(null);
 
  // Define the gradient blue for the text
  const gradientBlueText = {
    background: 'linear-gradient(135deg, #00a0e9 0%, #0056b3 100%)',
    WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    backgroundClip: 'text', // Standard property
    color: '#00a0e9', // Fallback color
  };
 
  const setExpandedIndex = (value) => {
    expandedIndexRef.current = value;
    _setExpandedIndex(value);
  };
 
  useEffect(() => {
    if (expandedIndex !== null) {
      swiperRef.current?.autoplay?.stop();
      clearTimeout(timeoutRef.current);
      timeoutRef.current = setTimeout(() => {
        setExpandedIndex(null);
        swiperRef.current?.autoplay?.start();
      }, 60000);
    }
    return () => clearTimeout(timeoutRef.current);
  }, [expandedIndex]);
 
  const handleReadToggle = (index) => {
    clearTimeout(timeoutRef.current);
 
    if (expandedIndexRef.current === index) {
      setExpandedIndex(null);
      timeoutRef.current = setTimeout(() => {
        if (expandedIndexRef.current === null && swiperRef.current?.autoplay) {
          swiperRef.current.autoplay.start();
        }
      }, 3000);
    } else {
      setExpandedIndex(index);
    }
  };
 
  // Define a constant for the left padding/margin to keep consistency
  const textHorizontalStartOffset = '15px';
 
  return (
    <div
      className="container-fluid section-padding"
      style={{
        background: "#0c1f38",
        minHeight: "100vh",
        paddingTop: "100px",
        overflowX: "hidden",
      }}
    >
      <h2
        className="text-center mb-2"
        style={{
                     fontSize: "3rem",
              fontWeight: "800",
              letterSpacing: "2.6px",
              marginBottom: "1rem",
              background: "linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
              backgroundClip: "text",
              textShadow: "0 0 30px rgba(0, 160, 233, 0.3)",
                    }}
      >
        What Our Clients Say
      </h2>
      <div
        style={{
          width: "60px",
          height: "4px",
          backgroundColor: "#00A0E9",
          margin: "0 auto 24px auto",
          borderRadius: "10px",
        }}
      ></div>
 
      <Swiper
        onSwiper={(swiper) => (swiperRef.current = swiper)}
        spaceBetween={20}
        loop={true}
        autoplay={{
          delay: 4000,
          disableOnInteraction: false,
        }}
        speed={800}
        modules={[Autoplay]}
        breakpoints={{
          0: { slidesPerView: 1.05 },
          576: { slidesPerView: 1.2 },
          768: { slidesPerView: 2 },
          992: { slidesPerView: 2.5 },
          1200: { slidesPerView: 3 },
        }}
        style={{ paddingBottom: "60px", paddingLeft: "15px" }}
      >
        {testimonials.map((item, index) => (
          <SwiperSlide
            key={index}
            style={{ display: "flex", justifyContent: "center" }}
          >
            <div
              style={{
                background: "#ffffff",
                borderRadius: "16px",
                padding: "20px 18px", // Card padding (inner content space)
                width: "100%",
                maxWidth: "340px",
                minHeight: "330px", // Keep minHeight for consistent card size
                display: "flex",
                flexDirection: "column",
                justifyContent: "space-between",
                boxShadow: "0 0 12px rgba(0,160,233,0.25)",
              }}
            >
              {/* Top Quote Section and Review Text */}
              <div
                style={{
                  textAlign: "left",
                  fontStyle: "italic",
                  fontWeight: 500,
                  position: 'relative',
                  flexGrow: 1,
                  minHeight: '160px', // ADDED/MODIFIED: Ensures consistent height for review area
                }}
              >
                {/* Opening Quote */}
                <span
                  style={{
                    fontSize: "22px",
                    color: "#000",
                    position: 'absolute',
                    top: '-10px',
                    left: `calc(-5px + ${textHorizontalStartOffset})`,
                    zIndex: 1,
                  }}
                >
                  “
                </span>
                <p
                  style={{
                    display: "-webkit-box",
                    WebkitBoxOrient: "vertical",
                    WebkitLineClamp: expandedIndex === index ? "unset" : 5,
                    overflow: "hidden",
                    fontSize: "0.95rem",
                    lineHeight: "1.7",
                    color: "#000",
                    margin: 0,
                    paddingLeft: textHorizontalStartOffset, // Main text content left alignment
                    paddingRight: textHorizontalStartOffset, // Main text content right alignment
                  }}
                >
                  {item.review}
                  {/* Closing Quote - inside the paragraph, hidden by WebkitLineClamp when truncated */}
                  <span style={{ fontSize: "22px", color: "#000", lineHeight: 0 }}>”</span>
                </p>
 
                {/* Optional Read More Toggle */}
                {item.review.length > 150 && (
                  <span
                    onClick={() => handleReadToggle(index)}
                    style={{
                      ...gradientBlueText,
                      fontWeight: "bold",
                      cursor: "pointer",
                      fontSize: "0.9rem",
                      display: 'inline-block',
                      marginTop: '5px', // Small margin to separate from text
                      marginLeft: textHorizontalStartOffset, // THIS ALIGNS IT WITH THE TEXT
                    }}
                  >
                    {expandedIndex === index ? "show less" : "...read more"}
                  </span>
                )}
              </div> {/* End of Top Quote Section and Review Text */}
 
              <hr style={{ color: "#000", margin: '15px 0' }}/>
 
              {/* NEW Bottom Section: Logo, Name, Role (all centered) */}
              <div
                style={{
                  display: "flex",
                  flexDirection: "column", // Stack items vertically
                  alignItems: "center", // ALIGNS ITEMS HORIZONTALLY IN THE CENTER
                  width: "100%",
                  padding: "0 5px", // Small padding to avoid content touching card edges
                  marginTop: 'auto', // Push this section to the bottom
                }}
              >
                {/* Company Logo */}
                {item.logo && ( // Only render logo if 'item.logo' is not null
                  <img
                    src={item.logo}
                    alt={`${item.name}'s Company Logo`}
                    style={{
                      maxWidth: "100px", // Adjusted max-width for logo as per reference
                      maxHeight: "50px", // Adjusted max-height for logo as per reference
                      objectFit: "contain",
                      marginBottom: '10px', // Space between logo and name
                      filter: 'none', // Removed grayscale filter
                      opacity: 1, // Full opacity
                      transition: 'none', // Removed hover effects
                      cursor: 'default',
                    }}
                  />
                )}
 
                {/* Reviewer Name */}
                <h6
                  className="mb-0 fw-bold"
                  style={{
                    color: "#000",
                    fontSize: "0.95rem",
                    textAlign: "center" // Ensures text within h6 is centered
                  }}
                >
                  {item.name}
                </h6>
                {/* Reviewer Role (including company name if part of the role string) */}
                <small
                  style={{
                    color: "#000",
                    fontSize: "0.8rem",
                    textAlign: "center"
                  }}
                >
                  {item.role}
                </small>
              </div>
            </div>
          </SwiperSlide>
        ))}
      </Swiper>
    </div>
  );
};
 
export default ClientTestimonials;